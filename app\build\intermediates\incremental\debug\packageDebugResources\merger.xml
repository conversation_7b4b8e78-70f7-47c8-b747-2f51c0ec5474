<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res"><file name="bg_message_ai" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\bg_message_ai.xml" qualifiers="" type="drawable"/><file name="bg_message_user" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\bg_message_user.xml" qualifiers="" type="drawable"/><file name="bg_operation_button" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\bg_operation_button.xml" qualifiers="" type="drawable"/><file name="bg_operation_buttons" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\bg_operation_buttons.xml" qualifiers="" type="drawable"/><file name="bg_send_button" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\bg_send_button.xml" qualifiers="" type="drawable"/><file name="bg_source_pill" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\bg_source_pill.xml" qualifiers="" type="drawable"/><file name="bg_voice_button" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\bg_voice_button.xml" qualifiers="" type="drawable"/><file name="circle_background" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="color_circle" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\color_circle.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_chat" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_chat.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_copy" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_copy.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_file_csv" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_file_csv.xml" qualifiers="" type="drawable"/><file name="ic_file_doc" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_file_doc.xml" qualifiers="" type="drawable"/><file name="ic_file_pdf" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_file_pdf.xml" qualifiers="" type="drawable"/><file name="ic_file_txt" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_file_txt.xml" qualifiers="" type="drawable"/><file name="ic_file_unknown" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_file_unknown.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_info" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_knowledge" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_knowledge.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_mic" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_mic.xml" qualifiers="" type="drawable"/><file name="ic_more_horiz" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_more_horiz.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_notifications" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_notifications.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_search" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_select_all" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_select_all.xml" qualifiers="" type="drawable"/><file name="ic_send" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_send.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_share" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/><file name="ic_upload" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_upload.xml" qualifiers="" type="drawable"/><file name="ic_volume_off" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_volume_off.xml" qualifiers="" type="drawable"/><file name="ic_volume_up" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_volume_up.xml" qualifiers="" type="drawable"/><file name="ios_badge_background" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ios_badge_background.xml" qualifiers="" type="drawable"/><file name="ios_button_primary" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ios_button_primary.xml" qualifiers="" type="drawable"/><file name="ios_button_secondary" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ios_button_secondary.xml" qualifiers="" type="drawable"/><file name="ios_card_background" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ios_card_background.xml" qualifiers="" type="drawable"/><file name="ios_input_background" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ios_input_background.xml" qualifiers="" type="drawable"/><file name="ios_upload_area" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ios_upload_area.xml" qualifiers="" type="drawable"/><file name="rag_icon" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\rag_icon.png" qualifiers="" type="drawable"/><file name="upload_area_background" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\upload_area_background.xml" qualifiers="" type="drawable"/><file name="activity_chat" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\activity_chat.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="dialog_create_group" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\dialog_create_group.xml" qualifiers="" type="layout"/><file name="dialog_edit_profile" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\dialog_edit_profile.xml" qualifiers="" type="layout"/><file name="dialog_knowledge_management" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\dialog_knowledge_management.xml" qualifiers="" type="layout"/><file name="fragment_chat" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_chat.xml" qualifiers="" type="layout"/><file name="fragment_conversation_groups" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_conversation_groups.xml" qualifiers="" type="layout"/><file name="fragment_conversation_list" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_conversation_list.xml" qualifiers="" type="layout"/><file name="fragment_knowledge" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_knowledge.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_system_settings" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_system_settings.xml" qualifiers="" type="layout"/><file name="fragment_theme_color_settings" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_theme_color_settings.xml" qualifiers="" type="layout"/><file name="fragment_theme_settings" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_theme_settings.xml" qualifiers="" type="layout"/><file name="fragment_voice_engine_settings" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\fragment_voice_engine_settings.xml" qualifiers="" type="layout"/><file name="item_conversation" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\item_conversation.xml" qualifiers="" type="layout"/><file name="item_conversation_group" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\item_conversation_group.xml" qualifiers="" type="layout"/><file name="item_document" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\item_document.xml" qualifiers="" type="layout"/><file name="item_message_ai" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\item_message_ai.xml" qualifiers="" type="layout"/><file name="item_message_user" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\item_message_user.xml" qualifiers="" type="layout"/><file name="item_nav_conversation" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\item_nav_conversation.xml" qualifiers="" type="layout"/><file name="item_nav_group" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\item_nav_group.xml" qualifiers="" type="layout"/><file name="navigation_drawer" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\navigation_drawer.xml" qualifiers="" type="layout"/><file name="ai_message_options_menu" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\menu\ai_message_options_menu.xml" qualifiers="" type="menu"/><file name="group_options_menu" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\menu\group_options_menu.xml" qualifiers="" type="menu"/><file name="message_context_menu" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\menu\message_context_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values\attrs.xml" qualifiers=""><attr format="color" name="colorThemePrimary"/><attr format="color" name="colorThemePrimaryDark"/><attr format="color" name="colorThemePrimaryLight"/><attr format="color" name="colorHeader"/><attr format="color" name="colorHeaderDark"/><attr format="color" name="colorAccent"/><attr format="color" name="colorTabSelected"/><attr format="color" name="colorUserMessageBg"/><attr format="color" name="colorProfileHeader"/><attr format="color" name="colorFileIconDoc"/><attr format="color" name="colorSendButton"/><attr format="color" name="colorSendButtonPressed"/><attr format="color" name="colorBadgeBackground"/><attr format="color" name="colorAppBackground"/><attr format="color" name="colorCardBackground"/><attr format="color" name="colorTextPrimary"/><attr format="color" name="colorTextSecondary"/><attr format="color" name="colorBorder"/></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="ios_blue">#007AFF</color><color name="ios_blue_dark">#0056CC</color><color name="ios_blue_light">#E3F2FD</color><color name="ios_red">#FF3B30</color><color name="ios_green">#34C759</color><color name="ios_orange">#FF9500</color><color name="ios_yellow">#FFCC00</color><color name="ios_purple">#AF52DE</color><color name="ios_background">#FFFFFF</color><color name="ios_background_secondary">#FFFFFF</color><color name="ios_background_tertiary">#FFFFFF</color><color name="ios_card_background">#FFFFFF</color><color name="ios_text_primary">#1C1C1E</color><color name="ios_text_secondary">#8E8E93</color><color name="ios_text_tertiary">#C7C7CC</color><color name="ios_text_white">#FFFFFF</color><color name="ios_separator">#C6C6C8</color><color name="ios_border">#E5E5EA</color><color name="chat_background">@color/ios_background</color><color name="user_message_bg">@color/ios_blue_light</color><color name="ai_message_bg">@color/ios_card_background</color><color name="header_blue">@color/ios_blue</color><color name="header_blue_dark">@color/ios_blue_dark</color><color name="text_gray">@color/ios_text_secondary</color><color name="text_dark">@color/ios_text_primary</color><color name="border_gray">@color/ios_border</color><color name="source_pill_bg">@color/ios_background_tertiary</color><color name="tab_selected">@color/ios_blue</color><color name="tab_unselected">@color/ios_text_secondary</color><color name="document_item_bg">@color/ios_card_background</color><color name="upload_area_bg">@color/ios_background_tertiary</color><color name="upload_border">@color/ios_border</color><color name="file_icon_pdf">@color/ios_red</color><color name="file_icon_doc">@color/ios_blue</color><color name="file_icon_csv">@color/ios_green</color><color name="file_icon_txt">@color/ios_purple</color><color name="profile_header_bg">@color/ios_blue</color><color name="setting_item_bg">@color/ios_card_background</color><color name="logout_button">@color/ios_red</color><color name="divider_color">@color/ios_separator</color><color name="gray_light">#F8F9FA</color><color name="gray_border">#E9ECEF</color><color name="gray_pressed">#E9ECEF</color><color name="gray_background">#FAFBFC</color><color name="gray_text">#6C757D</color><color name="gray_text_light">#ADB5BD</color><color name="theme_green">#34C759</color><color name="theme_green_dark">#28A745</color><color name="theme_green_light">#E8F5E8</color><color name="theme_red">#FF3B30</color><color name="theme_red_dark">#DC3545</color><color name="theme_red_light">#FFE8E8</color><color name="theme_purple">#AF52DE</color><color name="theme_purple_dark">#8E44AD</color><color name="theme_purple_light">#F3E8FF</color><color name="theme_orange">#FF9500</color><color name="theme_orange_dark">#E67E22</color><color name="theme_orange_light">#FFF3E0</color><color name="theme_pink">#FF2D92</color><color name="theme_pink_dark">#E91E63</color><color name="theme_pink_light">#FFE8F5</color><color name="theme_teal">#5AC8FA</color><color name="theme_indigo">#5856D6</color><color name="theme_brown">#A2845E</color><color name="voice_button_background">@color/ios_background_secondary</color><color name="voice_button_border">@color/ios_border</color><color name="voice_recording_background">#4CAF50</color><color name="voice_processing_background">#FF9800</color><color name="ios_background_unified">#FFFFFF</color><color name="theme_gray_dark">#6D6D70</color><color name="theme_gray_light">#F2F2F7</color><color name="theme_gray">#8E8E93</color><color name="text_secondary_soft">#6C757D</color><color name="background_primary">#F8F9FA</color><color name="background_secondary">#F1F3F4</color><color name="text_primary_strong">#1A1A1A</color><color name="shadow_medium">#16000000</color><color name="shadow_dark">#24000000</color><color name="background_elevated">#FFFFFF</color><color name="shadow_light">#08000000</color><color name="text_tertiary_light">#ADB5BD</color></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">RAG Android</string><string name="start_chat">开始聊天</string><string name="send">发送</string><string name="message_hint">输入您的消息...</string><string name="welcome_message">您好！我是您的AI助手，有什么可以帮助您的吗？</string><string name="voice_engine_settings">语音引擎设置</string><string name="voice_engine">语音引擎</string><string name="current_engine">当前引擎</string><string name="android_tts_engine">系统语音引擎</string><string name="gemini_tts_engine">Gemini语音引擎</string><string name="speech_rate">语速</string><string name="pitch">音调</string><string name="language">语言</string><string name="test_voice">测试语音</string><string name="stop_test">停止测试</string><string name="reset_settings">重置为默认设置</string><string name="engine_description">引擎说明</string><string name="select_voice_engine">选择语音引擎</string><string name="select_language">选择语言</string><string name="chinese_simplified">中文（简体）</string><string name="chinese_traditional">中文（繁体）</string><string name="english_us">英语（美国）</string><string name="english_uk">英语（英国）</string><string name="voice_test_text_zh">你好，这是语音测试。</string><string name="voice_test_text_en">Hello, this is a voice test.</string><string name="engine_switched">已切换到%s</string><string name="switch_engine_failed">切换引擎失败</string><string name="language_set">语言已设置为%s</string><string name="set_language_failed">设置语言失败</string><string name="voice_test_failed">语音测试失败</string><string name="reset_confirm_title">重置设置</string><string name="reset_confirm_message">确定要将所有语音设置重置为默认值吗？</string><string name="settings_reset">设置已重置为默认值</string><string name="back">返回</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="auto_play_ai_reply">AI回复自动播放</string><string name="auto_play_ai_reply_desc">开启后AI回复时将自动播放语音</string><string name="auto_play_enabled">AI回复自动播放已开启</string><string name="auto_play_disabled">AI回复自动播放已关闭</string></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Ragandroid" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowTranslucentStatus" ns1:targetApi="kitkat">false</item>
        
        
        <item name="android:windowSoftInputMode">adjustResize</item>

        
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style><style name="Theme.Ragandroid.Blue" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style><style name="Theme.Ragandroid.Green" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_green</item>
        <item name="colorThemePrimaryDark">@color/theme_green_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_green_light</item>
        <item name="colorHeader">@color/theme_green</item>
        <item name="colorHeaderDark">@color/theme_green_dark</item>
        <item name="colorAccent">@color/theme_green</item>
        <item name="colorTabSelected">@color/theme_green</item>
        <item name="colorUserMessageBg">@color/theme_green</item>
        <item name="colorProfileHeader">@color/theme_green</item>
        <item name="colorFileIconDoc">@color/theme_green</item>
        <item name="colorSendButton">@color/theme_green</item>
        <item name="colorSendButtonPressed">@color/theme_green_dark</item>
        <item name="colorBadgeBackground">@color/theme_green</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style><style name="Theme.Ragandroid.Red" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_red</item>
        <item name="colorThemePrimaryDark">@color/theme_red_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_red_light</item>
        <item name="colorHeader">@color/theme_red</item>
        <item name="colorHeaderDark">@color/theme_red_dark</item>
        <item name="colorAccent">@color/theme_red</item>
        <item name="colorTabSelected">@color/theme_red</item>
        <item name="colorUserMessageBg">@color/theme_red</item>
        <item name="colorProfileHeader">@color/theme_red</item>
        <item name="colorFileIconDoc">@color/theme_red</item>
        <item name="colorSendButton">@color/theme_red</item>
        <item name="colorSendButtonPressed">@color/theme_red_dark</item>
        <item name="colorBadgeBackground">@color/theme_red</item>
    </style><style name="Theme.Ragandroid.Purple" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_purple</item>
        <item name="colorThemePrimaryDark">@color/theme_purple_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_purple_light</item>
        <item name="colorHeader">@color/theme_purple</item>
        <item name="colorHeaderDark">@color/theme_purple_dark</item>
        <item name="colorAccent">@color/theme_purple</item>
        <item name="colorTabSelected">@color/theme_purple</item>
        <item name="colorUserMessageBg">@color/theme_purple</item>
        <item name="colorProfileHeader">@color/theme_purple</item>
        <item name="colorFileIconDoc">@color/theme_purple</item>
        <item name="colorSendButton">@color/theme_purple</item>
        <item name="colorSendButtonPressed">@color/theme_purple_dark</item>
        <item name="colorBadgeBackground">@color/theme_purple</item>
    </style><style name="Theme.Ragandroid.Orange" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_orange</item>
        <item name="colorThemePrimaryDark">@color/theme_orange_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_orange_light</item>
        <item name="colorHeader">@color/theme_orange</item>
        <item name="colorHeaderDark">@color/theme_orange_dark</item>
        <item name="colorAccent">@color/theme_orange</item>
        <item name="colorTabSelected">@color/theme_orange</item>
        <item name="colorUserMessageBg">@color/theme_orange</item>
        <item name="colorProfileHeader">@color/theme_orange</item>
        <item name="colorFileIconDoc">@color/theme_orange</item>
        <item name="colorSendButton">@color/theme_orange</item>
        <item name="colorSendButtonPressed">@color/theme_orange_dark</item>
        <item name="colorBadgeBackground">@color/theme_orange</item>
    </style><style name="Theme.Ragandroid.Pink" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_pink</item>
        <item name="colorThemePrimaryDark">@color/theme_pink_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_pink_light</item>
        <item name="colorHeader">@color/theme_pink</item>
        <item name="colorHeaderDark">@color/theme_pink_dark</item>
        <item name="colorAccent">@color/theme_pink</item>
        <item name="colorTabSelected">@color/theme_pink</item>
        <item name="colorUserMessageBg">@color/theme_pink</item>
        <item name="colorProfileHeader">@color/theme_pink</item>
        <item name="colorFileIconDoc">@color/theme_pink</item>
        <item name="colorSendButton">@color/theme_pink</item>
        <item name="colorSendButtonPressed">@color/theme_pink_dark</item>
        <item name="colorBadgeBackground">@color/theme_pink</item>
    </style><style name="Theme.Ragandroid.Gray" parent="Theme.Ragandroid">
        <item name="colorThemePrimary">@color/theme_gray</item>
        <item name="colorThemePrimaryDark">@color/theme_gray_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_gray_light</item>
        <item name="colorHeader">@color/theme_gray</item>
        <item name="colorHeaderDark">@color/theme_gray_dark</item>
        <item name="colorAccent">@color/theme_gray</item>
        <item name="colorTabSelected">@color/theme_gray</item>
        <item name="colorUserMessageBg">@color/theme_gray</item>
        <item name="colorProfileHeader">@color/theme_gray</item>
        <item name="colorFileIconDoc">@color/theme_gray</item>
        <item name="colorSendButton">@color/theme_gray</item>
        <item name="colorSendButtonPressed">@color/theme_gray_dark</item>
        <item name="colorBadgeBackground">@color/theme_gray</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="ios_background">#1C1C1E</color><color name="ios_background_secondary">#2C2C2E</color><color name="ios_background_tertiary">#3A3A3C</color><color name="ios_card_background">#1C1C1E</color><color name="ios_text_primary">#FFFFFF</color><color name="ios_text_secondary">#AEAEB2</color><color name="ios_text_tertiary">#8E8E93</color><color name="ios_separator">#38383A</color><color name="ios_border">#48484A</color><color name="chat_background">#1C1C1E</color><color name="ai_message_bg">#2C2C2E</color><color name="text_gray">#AEAEB2</color><color name="text_dark">#FFFFFF</color><color name="border_gray">#48484A</color><color name="source_pill_bg">#3A3A3C</color><color name="setting_item_bg">#2C2C2E</color><color name="document_item_bg">#2C2C2E</color><color name="upload_area_bg">#3A3A3C</color><color name="upload_border">#48484A</color><color name="gray_light">#3A3A3C</color><color name="gray_border">#48484A</color><color name="gray_pressed">#48484A</color><color name="gray_background">#1C1C1E</color><color name="gray_text">#AEAEB2</color><color name="gray_text_light">#8E8E93</color><color name="ios_background_unified">#1C1C1E</color><color name="theme_gray">#AEAEB2</color><color name="theme_gray_light">#3A3A3C</color><color name="theme_gray_dark">#8E8E93</color></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Ragandroid" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">false</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowTranslucentStatus" ns1:targetApi="kitkat">false</item>
        
        <item name="android:windowSoftInputMode">adjustResize</item>

        
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style><style name="Theme.Ragandroid.Gray" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
        <item name="android:windowSoftInputMode">adjustResize</item>

        
        <item name="colorThemePrimary">@color/theme_gray</item>
        <item name="colorThemePrimaryDark">@color/theme_gray_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_gray_light</item>
        <item name="colorHeader">@color/theme_gray</item>
        <item name="colorHeaderDark">@color/theme_gray_dark</item>
        <item name="colorAccent">@color/theme_gray</item>
        <item name="colorTabSelected">@color/theme_gray</item>
        <item name="colorUserMessageBg">@color/theme_gray</item>
        <item name="colorProfileHeader">@color/theme_gray</item>
        <item name="colorFileIconDoc">@color/theme_gray</item>
        <item name="colorSendButton">@color/theme_gray</item>
        <item name="colorSendButtonPressed">@color/theme_gray_dark</item>
        <item name="colorBadgeBackground">@color/theme_gray</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style></file><file name="backup_rules" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppPageRootLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:orientation">vertical</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:background">?attr/colorAppBackground</item>
    </style><style name="AppHeaderLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">?attr/colorHeader</item>
        <item name="android:elevation">4dp</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:paddingTop">@dimen/status_bar_height</item>
    </style><style name="AppHeaderContent">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingHorizontal">20dp</item>
    </style><style name="AppBackButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:src">@drawable/ic_arrow_back</item>
        <item name="android:tint">@color/white</item>
        <item name="android:contentDescription">返回</item>
        <item name="android:padding">12dp</item>
    </style><style name="AppHeaderTitle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style><style name="AppHeaderSpacer">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
    </style><style name="AppContentLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
    </style><style name="AppScrollView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:fillViewport">true</item>
        <item name="android:overScrollMode">ifContentScrolls</item>
    </style></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="status_bar_height">25dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="nav_header_height">176dp</dimen><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="fab_margin">16dp</dimen><dimen name="text_margin">16dp</dimen></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values\colors_statusbar.xml" qualifiers=""><color name="status_bar_light_blue">#E3F2FD</color><color name="status_bar_light_green">#E8F5E8</color><color name="status_bar_light_red">#FFEBEE</color><color name="status_bar_light_purple">#F3E5F5</color><color name="status_bar_light_orange">#FFF3E0</color><color name="status_bar_light_pink">#FCE4EC</color><color name="status_bar_light_gray">#F5F5F5</color><color name="status_bar_dark_blue">#1565C0</color><color name="status_bar_dark_green">#2E7D32</color><color name="status_bar_dark_red">#C62828</color><color name="status_bar_dark_purple">#7B1FA2</color><color name="status_bar_dark_orange">#EF6C00</color><color name="status_bar_dark_pink">#C2185B</color><color name="status_bar_dark_gray">#424242</color><color name="status_bar_text_light">#1C1B1F</color><color name="status_bar_text_dark">#E6E1E5</color><color name="status_bar_overlay_light">#1A000000</color><color name="status_bar_overlay_dark">#1AFFFFFF</color></file><file path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\values-night\colors_statusbar.xml" qualifiers="night-v8"><color name="status_bar_light_blue">#0D47A1</color><color name="status_bar_light_green">#1B5E20</color><color name="status_bar_light_red">#B71C1C</color><color name="status_bar_light_purple">#4A148C</color><color name="status_bar_light_orange">#E65100</color><color name="status_bar_light_pink">#880E4F</color><color name="status_bar_light_gray">#212121</color><color name="status_bar_dark_blue">#1565C0</color><color name="status_bar_dark_green">#2E7D32</color><color name="status_bar_dark_red">#C62828</color><color name="status_bar_dark_purple">#7B1FA2</color><color name="status_bar_dark_orange">#EF6C00</color><color name="status_bar_dark_pink">#C2185B</color><color name="status_bar_dark_gray">#424242</color><color name="status_bar_text_light">#E6E1E5</color><color name="status_bar_text_dark">#1C1B1F</color></file><file name="ic_bug_report" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\drawable\ic_bug_report.xml" qualifiers="" type="drawable"/><file name="activity_status_bar_test" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\layout\activity_status_bar_test.xml" qualifiers="" type="layout"/><file name="debug_menu" path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\main\res\menu\debug_menu.xml" qualifiers="" type="menu"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CodeBase\AndroidCode\RAG-Andorid\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CodeBase\AndroidCode\RAG-Andorid\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\CodeBase\AndroidCode\RAG-Andorid\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>