<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 系统默认颜色 -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- 现代化渐变和阴影效果颜色 -->
    <color name="shadow_light">#08000000</color>
    <color name="shadow_medium">#16000000</color>
    <color name="shadow_dark">#24000000</color>
    
    <!-- 现代化背景色 -->
    <color name="background_elevated">#FFFFFF</color>
    <color name="background_primary">#F8F9FA</color>
    <color name="background_secondary">#F1F3F4</color>
    
    <!-- 改进的文本颜色 -->
    <color name="text_primary_strong">#1A1A1A</color>
    <color name="text_secondary_soft">#6C757D</color>
    <color name="text_tertiary_light">#ADB5BD</color>

    <!-- iOS 风格主色调 -->
    <color name="ios_blue">#007AFF</color>
    <color name="ios_blue_dark">#0056CC</color>
    <color name="ios_blue_light">#E3F2FD</color>
    <color name="ios_red">#FF3B30</color>
    <color name="ios_green">#34C759</color>
    <color name="ios_orange">#FF9500</color>
    <color name="ios_yellow">#FFCC00</color>
    <color name="ios_purple">#AF52DE</color>

    <!-- iOS 风格背景色 -->
    <color name="ios_background">#FFFFFF</color>
    <color name="ios_background_secondary">#FFFFFF</color>
    <color name="ios_background_tertiary">#F8F8F8</color>
    <color name="ios_card_background">#F2F2F7</color>
    <color name="ios_background_unified">#F2F2F7</color>

    <!-- iOS 风格文字颜色 -->
    <color name="ios_text_primary">#1C1C1E</color>
    <color name="ios_text_secondary">#8E8E93</color>
    <color name="ios_text_tertiary">#C7C7CC</color>
    <color name="ios_text_white">#FFFFFF</color>

    <!-- iOS 风格分割线和边框 -->
    <color name="ios_separator">#C6C6C8</color>
    <color name="ios_border">#E5E5EA</color>

    <!-- 应用主题色映射 - 保持向后兼容 -->
    <color name="chat_background">@color/ios_background</color>
    <color name="user_message_bg">@color/ios_blue_light</color>
    <color name="ai_message_bg">@color/ios_card_background</color>
    <color name="header_blue">@color/ios_blue</color>
    <color name="header_blue_dark">@color/ios_blue_dark</color>
    <color name="text_gray">@color/ios_text_secondary</color>
    <color name="text_dark">@color/ios_text_primary</color>
    <color name="border_gray">@color/ios_border</color>
    <color name="source_pill_bg">@color/ios_background_tertiary</color>

    <!-- 底部导航颜色 -->
    <color name="tab_selected">@color/ios_blue</color>
    <color name="tab_unselected">@color/ios_text_secondary</color>

    <!-- 知识库颜色 -->
    <color name="document_item_bg">@color/ios_card_background</color>
    <color name="upload_area_bg">@color/ios_background_tertiary</color>
    <color name="upload_border">@color/ios_border</color>
    <color name="file_icon_pdf">@color/ios_red</color>
    <color name="file_icon_doc">@color/ios_blue</color>
    <color name="file_icon_csv">@color/ios_green</color>
    <color name="file_icon_txt">@color/ios_purple</color>

    <!-- 个人中心颜色 -->
    <color name="profile_header_bg">@color/ios_blue</color>
    <color name="setting_item_bg">@color/ios_card_background</color>
    <color name="logout_button">@color/ios_red</color>
    <color name="divider_color">@color/ios_separator</color>

    <!-- DeepSeek风格灰白主题色 -->
    <color name="gray_light">#F8F9FA</color>
    <color name="gray_border">#E9ECEF</color>
    <color name="gray_pressed">#E9ECEF</color>
    <color name="gray_background">#FAFBFC</color>
    <color name="gray_text">#6C757D</color>
    <color name="gray_text_light">#ADB5BD</color>

    <!-- 主题色选项 -->
    <color name="theme_green">#34C759</color>
    <color name="theme_green_dark">#28A745</color>
    <color name="theme_green_light">#E8F5E8</color>

    <color name="theme_red">#FF3B30</color>
    <color name="theme_red_dark">#DC3545</color>
    <color name="theme_red_light">#FFE8E8</color>

    <color name="theme_purple">#AF52DE</color>
    <color name="theme_purple_dark">#8E44AD</color>
    <color name="theme_purple_light">#F3E8FF</color>

    <color name="theme_orange">#FF9500</color>
    <color name="theme_orange_dark">#E67E22</color>
    <color name="theme_orange_light">#FFF3E0</color>

    <color name="theme_pink">#FF2D92</color>
    <color name="theme_pink_dark">#E91E63</color>
    <color name="theme_pink_light">#FFE8F5</color>

    <color name="theme_gray">#8E8E93</color>
    <color name="theme_gray_dark">#6D6D70</color>
    <color name="theme_gray_light">#F2F2F7</color>

    <color name="theme_teal">#5AC8FA</color>
    <color name="theme_indigo">#5856D6</color>
    <color name="theme_brown">#A2845E</color>

    <!-- 语音功能颜色 -->
    <color name="voice_button_background">@color/ios_background_secondary</color>
    <color name="voice_button_border">@color/ios_border</color>
    <color name="voice_recording_background">#4CAF50</color>
    <color name="voice_processing_background">#FF9800</color>
</resources>