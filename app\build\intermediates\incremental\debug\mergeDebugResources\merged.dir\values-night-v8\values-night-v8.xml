<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="ai_message_bg">#2C2C2E</color>
    <color name="border_gray">#48484A</color>
    <color name="chat_background">#1C1C1E</color>
    <color name="document_item_bg">#2C2C2E</color>
    <color name="gray_background">#1C1C1E</color>
    <color name="gray_border">#48484A</color>
    <color name="gray_light">#3A3A3C</color>
    <color name="gray_pressed">#48484A</color>
    <color name="gray_text">#AEAEB2</color>
    <color name="gray_text_light">#8E8E93</color>
    <color name="ios_background">#1C1C1E</color>
    <color name="ios_background_secondary">#2C2C2E</color>
    <color name="ios_background_tertiary">#3A3A3C</color>
    <color name="ios_background_unified">#1C1C1E</color>
    <color name="ios_border">#48484A</color>
    <color name="ios_card_background">#1C1C1E</color>
    <color name="ios_separator">#38383A</color>
    <color name="ios_text_primary">#FFFFFF</color>
    <color name="ios_text_secondary">#AEAEB2</color>
    <color name="ios_text_tertiary">#8E8E93</color>
    <color name="setting_item_bg">#2C2C2E</color>
    <color name="source_pill_bg">#3A3A3C</color>
    <color name="status_bar_dark_blue">#1565C0</color>
    <color name="status_bar_dark_gray">#424242</color>
    <color name="status_bar_dark_green">#2E7D32</color>
    <color name="status_bar_dark_orange">#EF6C00</color>
    <color name="status_bar_dark_pink">#C2185B</color>
    <color name="status_bar_dark_purple">#7B1FA2</color>
    <color name="status_bar_dark_red">#C62828</color>
    <color name="status_bar_light_blue">#0D47A1</color>
    <color name="status_bar_light_gray">#212121</color>
    <color name="status_bar_light_green">#1B5E20</color>
    <color name="status_bar_light_orange">#E65100</color>
    <color name="status_bar_light_pink">#880E4F</color>
    <color name="status_bar_light_purple">#4A148C</color>
    <color name="status_bar_light_red">#B71C1C</color>
    <color name="status_bar_text_dark">#1C1B1F</color>
    <color name="status_bar_text_light">#E6E1E5</color>
    <color name="text_dark">#FFFFFF</color>
    <color name="text_gray">#AEAEB2</color>
    <color name="theme_gray">#AEAEB2</color>
    <color name="theme_gray_dark">#8E8E93</color>
    <color name="theme_gray_light">#3A3A3C</color>
    <color name="upload_area_bg">#3A3A3C</color>
    <color name="upload_border">#48484A</color>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.NoActionBar"/>
    <style name="Theme.Material3.DayNight" parent="Theme.Material3.Dark"/>
    <style name="Theme.Material3.DayNight.BottomSheetDialog" parent="Theme.Material3.Dark.BottomSheetDialog"/>
    <style name="Theme.Material3.DayNight.Dialog" parent="Theme.Material3.Dark.Dialog"/>
    <style name="Theme.Material3.DayNight.Dialog.Alert" parent="Theme.Material3.Dark.Dialog.Alert"/>
    <style name="Theme.Material3.DayNight.Dialog.MinWidth" parent="Theme.Material3.Dark.Dialog.MinWidth"/>
    <style name="Theme.Material3.DayNight.DialogWhenLarge" parent="Theme.Material3.Dark.DialogWhenLarge"/>
    <style name="Theme.Material3.DayNight.NoActionBar" parent="Theme.Material3.Dark.NoActionBar"/>
    <style name="Theme.Material3.DayNight.SideSheetDialog" parent="Theme.Material3.Dark.SideSheetDialog"/>
    <style name="Theme.Material3.DynamicColors.DayNight" parent="Theme.Material3.DynamicColors.Dark"/>
    <style name="Theme.Material3.DynamicColors.DayNight.NoActionBar" parent="Theme.Material3.DynamicColors.Dark.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="Theme.MaterialComponents.BottomSheetDialog"/>
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="Theme.MaterialComponents.Dialog"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="Theme.MaterialComponents.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="Theme.MaterialComponents.Dialog.Alert.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="Theme.MaterialComponents.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="Theme.MaterialComponents.Dialog.FixedSize.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="Theme.MaterialComponents.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="Theme.MaterialComponents.Dialog.MinWidth.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="Theme.MaterialComponents.DialogWhenLarge"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="Theme.MaterialComponents.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="Theme.MaterialComponents.NoActionBar.Bridge"/>
    <style name="Theme.Ragandroid" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="m">false</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowTranslucentStatus" ns1:targetApi="kitkat">false</item>
        
        <item name="android:windowSoftInputMode">adjustResize</item>

        
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
    <style name="Theme.Ragandroid.Gray" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
        <item name="android:windowSoftInputMode">adjustResize</item>

        
        <item name="colorThemePrimary">@color/theme_gray</item>
        <item name="colorThemePrimaryDark">@color/theme_gray_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_gray_light</item>
        <item name="colorHeader">@color/theme_gray</item>
        <item name="colorHeaderDark">@color/theme_gray_dark</item>
        <item name="colorAccent">@color/theme_gray</item>
        <item name="colorTabSelected">@color/theme_gray</item>
        <item name="colorUserMessageBg">@color/theme_gray</item>
        <item name="colorProfileHeader">@color/theme_gray</item>
        <item name="colorFileIconDoc">@color/theme_gray</item>
        <item name="colorSendButton">@color/theme_gray</item>
        <item name="colorSendButtonPressed">@color/theme_gray_dark</item>
        <item name="colorBadgeBackground">@color/theme_gray</item>

        
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.Material3.DynamicColors.DayNight" parent="ThemeOverlay.Material3.DynamicColors.Dark"/>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="Widget.MaterialComponents.ActionBar.Surface"/>
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="Widget.MaterialComponents.AppBarLayout.Surface"/>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="Widget.MaterialComponents.BottomAppBar"/>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="Widget.MaterialComponents.BottomNavigationView"/>
    <style name="Widget.MaterialComponents.NavigationRailView.PrimarySurface" parent="Widget.MaterialComponents.NavigationRailView"/>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="Widget.MaterialComponents.TabLayout"/>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="Widget.MaterialComponents.Toolbar.Surface"/>
</resources>