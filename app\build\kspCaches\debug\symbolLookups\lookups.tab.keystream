  
ValueAnimator android.animation  Activity android.app  Button android.app.Activity  ChatAdapter android.app.Activity  EditText android.app.Activity  ImageButton android.app.Activity  LinearLayout android.app.Activity  Markwon android.app.Activity  RecyclerView android.app.Activity  
ScrollView android.app.Activity  StatusBarManager android.app.Activity  StatusBarTestHelper android.app.Activity  TextView android.app.Activity  ThemeManager android.app.Activity  Context android.content  SharedPreferences android.content  Button android.content.Context  ChatAdapter android.content.Context  EditText android.content.Context  ImageButton android.content.Context  LinearLayout android.content.Context  Markwon android.content.Context  RecyclerView android.content.Context  
ScrollView android.content.Context  StatusBarManager android.content.Context  StatusBarTestHelper android.content.Context  TextView android.content.Context  ThemeManager android.content.Context  Button android.content.ContextWrapper  ChatAdapter android.content.ContextWrapper  EditText android.content.ContextWrapper  ImageButton android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  Markwon android.content.ContextWrapper  RecyclerView android.content.ContextWrapper  
ScrollView android.content.ContextWrapper  StatusBarManager android.content.ContextWrapper  StatusBarTestHelper android.content.ContextWrapper  TextView android.content.ContextWrapper  ThemeManager android.content.ContextWrapper  GradientDrawable android.graphics.drawable  AudioRecord 
android.media  MediaPlayer 
android.media  TextToSpeech android.speech.tts  OnInitListener android.speech.tts.TextToSpeech  View android.view  	ViewGroup android.view  Window android.view  Button  android.view.ContextThemeWrapper  ChatAdapter  android.view.ContextThemeWrapper  EditText  android.view.ContextThemeWrapper  ImageButton  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  Markwon  android.view.ContextThemeWrapper  RecyclerView  android.view.ContextThemeWrapper  
ScrollView  android.view.ContextThemeWrapper  StatusBarManager  android.view.ContextThemeWrapper  StatusBarTestHelper  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  ThemeManager  android.view.ContextThemeWrapper  Button android.widget  EditText android.widget  ImageButton android.widget  	ImageView android.widget  LinearLayout android.widget  ProgressBar android.widget  RadioButton android.widget  
ScrollView android.widget  SeekBar android.widget  Switch android.widget  TextView android.widget  Button #androidx.activity.ComponentActivity  ChatAdapter #androidx.activity.ComponentActivity  EditText #androidx.activity.ComponentActivity  ImageButton #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  Markwon #androidx.activity.ComponentActivity  RecyclerView #androidx.activity.ComponentActivity  
ScrollView #androidx.activity.ComponentActivity  StatusBarManager #androidx.activity.ComponentActivity  StatusBarTestHelper #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  ThemeManager #androidx.activity.ComponentActivity  ColorInt androidx.annotation  AppCompatActivity androidx.appcompat.app  Button (androidx.appcompat.app.AppCompatActivity  ChatAdapter (androidx.appcompat.app.AppCompatActivity  EditText (androidx.appcompat.app.AppCompatActivity  ImageButton (androidx.appcompat.app.AppCompatActivity  LinearLayout (androidx.appcompat.app.AppCompatActivity  Markwon (androidx.appcompat.app.AppCompatActivity  RecyclerView (androidx.appcompat.app.AppCompatActivity  
ScrollView (androidx.appcompat.app.AppCompatActivity  StatusBarManager (androidx.appcompat.app.AppCompatActivity  StatusBarTestHelper (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  ThemeManager (androidx.appcompat.app.AppCompatActivity  SwitchCompat androidx.appcompat.widget  Button #androidx.core.app.ComponentActivity  ChatAdapter #androidx.core.app.ComponentActivity  EditText #androidx.core.app.ComponentActivity  ImageButton #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  Markwon #androidx.core.app.ComponentActivity  RecyclerView #androidx.core.app.ComponentActivity  
ScrollView #androidx.core.app.ComponentActivity  StatusBarManager #androidx.core.app.ComponentActivity  StatusBarTestHelper #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  ThemeManager #androidx.core.app.ComponentActivity  DrawerLayout androidx.drawerlayout.widget  Fragment androidx.fragment.app  Button androidx.fragment.app.Fragment  ChatAdapter androidx.fragment.app.Fragment  ChatRepository androidx.fragment.app.Fragment  ConversationAdapter androidx.fragment.app.Fragment  ConversationGroupAdapter androidx.fragment.app.Fragment  ConversationGroupRepository androidx.fragment.app.Fragment  DataManager androidx.fragment.app.Fragment  DocumentAdapter androidx.fragment.app.Fragment  DocumentRepository androidx.fragment.app.Fragment  DrawerLayout androidx.fragment.app.Fragment  EditText androidx.fragment.app.Fragment  ImageButton androidx.fragment.app.Fragment  	ImageView androidx.fragment.app.Fragment  Job androidx.fragment.app.Fragment  KnowledgeBaseManager androidx.fragment.app.Fragment  LinearLayout androidx.fragment.app.Fragment  Long androidx.fragment.app.Fragment  Markwon androidx.fragment.app.Fragment  NavConversationAdapter androidx.fragment.app.Fragment  NavGroupAdapter androidx.fragment.app.Fragment  OpenRouterApiService androidx.fragment.app.Fragment  ProgressBar androidx.fragment.app.Fragment  RadioButton androidx.fragment.app.Fragment  RagQueryEngine androidx.fragment.app.Fragment  RecyclerView androidx.fragment.app.Fragment  SeekBar androidx.fragment.app.Fragment  SharedPreferences androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  Switch androidx.fragment.app.Fragment  SwitchCompat androidx.fragment.app.Fragment  TextToSpeechService androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  ThemeManager androidx.fragment.app.Fragment  	TtsConfig androidx.fragment.app.Fragment  UserRepository androidx.fragment.app.Fragment  VoiceToTextService androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  Button &androidx.fragment.app.FragmentActivity  ChatAdapter &androidx.fragment.app.FragmentActivity  EditText &androidx.fragment.app.FragmentActivity  ImageButton &androidx.fragment.app.FragmentActivity  LinearLayout &androidx.fragment.app.FragmentActivity  Markwon &androidx.fragment.app.FragmentActivity  RecyclerView &androidx.fragment.app.FragmentActivity  
ScrollView &androidx.fragment.app.FragmentActivity  StatusBarManager &androidx.fragment.app.FragmentActivity  StatusBarTestHelper &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  ThemeManager &androidx.fragment.app.FragmentActivity  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  ChatMessage 1androidx.recyclerview.widget.RecyclerView.Adapter  Conversation 1androidx.recyclerview.widget.RecyclerView.Adapter  ConversationGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  ConversationItem 1androidx.recyclerview.widget.RecyclerView.Adapter  DocumentEntity 1androidx.recyclerview.widget.RecyclerView.Adapter  ImageButton 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 1androidx.recyclerview.widget.RecyclerView.Adapter  Markwon 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  ImageButton 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	ImageView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AppDatabase androidx.room.RoomDatabase  Callback androidx.room.RoomDatabase  ChatMessageDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  ConversationGroupDao androidx.room.RoomDatabase  DocumentDao androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  RoomDatabase androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  UserDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  SupportSQLiteDatabase #androidx.room.RoomDatabase.Callback  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  ChatAdapter com.bei.rag.ChatScreen  EditText com.bei.rag.ChatScreen  ImageButton com.bei.rag.ChatScreen  LinearLayout com.bei.rag.ChatScreen  Markwon com.bei.rag.ChatScreen  RecyclerView com.bei.rag.ChatScreen  StatusBarManager com.bei.rag.MainActivity  ThemeManager com.bei.rag.MainActivity  ChatAdapter com.bei.rag.adapter  ConversationAdapter com.bei.rag.adapter  ConversationGroupAdapter com.bei.rag.adapter  ConversationItem com.bei.rag.adapter  DocumentAdapter com.bei.rag.adapter  NavConversationAdapter com.bei.rag.adapter  NavGroupAdapter com.bei.rag.adapter  Unit com.bei.rag.adapter  ChatMessage com.bei.rag.adapter.ChatAdapter  ImageButton com.bei.rag.adapter.ChatAdapter  Markwon com.bei.rag.adapter.ChatAdapter  RecyclerView com.bei.rag.adapter.ChatAdapter  TextView com.bei.rag.adapter.ChatAdapter  Unit com.bei.rag.adapter.ChatAdapter  View com.bei.rag.adapter.ChatAdapter  ImageButton 3com.bei.rag.adapter.ChatAdapter.AiMessageViewHolder  TextView 3com.bei.rag.adapter.ChatAdapter.AiMessageViewHolder  View 3com.bei.rag.adapter.ChatAdapter.AiMessageViewHolder  ChatMessage )com.bei.rag.adapter.ChatAdapter.Companion  ImageButton )com.bei.rag.adapter.ChatAdapter.Companion  Markwon )com.bei.rag.adapter.ChatAdapter.Companion  RecyclerView )com.bei.rag.adapter.ChatAdapter.Companion  TextView )com.bei.rag.adapter.ChatAdapter.Companion  Unit )com.bei.rag.adapter.ChatAdapter.Companion  View )com.bei.rag.adapter.ChatAdapter.Companion  TextView 5com.bei.rag.adapter.ChatAdapter.UserMessageViewHolder  View 5com.bei.rag.adapter.ChatAdapter.UserMessageViewHolder  Conversation 'com.bei.rag.adapter.ConversationAdapter  ConversationViewHolder 'com.bei.rag.adapter.ConversationAdapter  ImageButton 'com.bei.rag.adapter.ConversationAdapter  RecyclerView 'com.bei.rag.adapter.ConversationAdapter  TextView 'com.bei.rag.adapter.ConversationAdapter  Unit 'com.bei.rag.adapter.ConversationAdapter  View 'com.bei.rag.adapter.ConversationAdapter  ImageButton >com.bei.rag.adapter.ConversationAdapter.ConversationViewHolder  TextView >com.bei.rag.adapter.ConversationAdapter.ConversationViewHolder  View >com.bei.rag.adapter.ConversationAdapter.ConversationViewHolder  ConversationGroup ,com.bei.rag.adapter.ConversationGroupAdapter  GroupViewHolder ,com.bei.rag.adapter.ConversationGroupAdapter  ImageButton ,com.bei.rag.adapter.ConversationGroupAdapter  RecyclerView ,com.bei.rag.adapter.ConversationGroupAdapter  TextView ,com.bei.rag.adapter.ConversationGroupAdapter  Unit ,com.bei.rag.adapter.ConversationGroupAdapter  View ,com.bei.rag.adapter.ConversationGroupAdapter  ImageButton <com.bei.rag.adapter.ConversationGroupAdapter.GroupViewHolder  TextView <com.bei.rag.adapter.ConversationGroupAdapter.GroupViewHolder  View <com.bei.rag.adapter.ConversationGroupAdapter.GroupViewHolder  DocumentEntity #com.bei.rag.adapter.DocumentAdapter  DocumentViewHolder #com.bei.rag.adapter.DocumentAdapter  ImageButton #com.bei.rag.adapter.DocumentAdapter  	ImageView #com.bei.rag.adapter.DocumentAdapter  RecyclerView #com.bei.rag.adapter.DocumentAdapter  TextView #com.bei.rag.adapter.DocumentAdapter  Unit #com.bei.rag.adapter.DocumentAdapter  View #com.bei.rag.adapter.DocumentAdapter  ImageButton 6com.bei.rag.adapter.DocumentAdapter.DocumentViewHolder  	ImageView 6com.bei.rag.adapter.DocumentAdapter.DocumentViewHolder  TextView 6com.bei.rag.adapter.DocumentAdapter.DocumentViewHolder  View 6com.bei.rag.adapter.DocumentAdapter.DocumentViewHolder  ConversationItem *com.bei.rag.adapter.NavConversationAdapter  NavConversationViewHolder *com.bei.rag.adapter.NavConversationAdapter  RecyclerView *com.bei.rag.adapter.NavConversationAdapter  TextView *com.bei.rag.adapter.NavConversationAdapter  Unit *com.bei.rag.adapter.NavConversationAdapter  View *com.bei.rag.adapter.NavConversationAdapter  TextView Dcom.bei.rag.adapter.NavConversationAdapter.NavConversationViewHolder  View Dcom.bei.rag.adapter.NavConversationAdapter.NavConversationViewHolder  ConversationGroup #com.bei.rag.adapter.NavGroupAdapter  NavGroupViewHolder #com.bei.rag.adapter.NavGroupAdapter  RecyclerView #com.bei.rag.adapter.NavGroupAdapter  TextView #com.bei.rag.adapter.NavGroupAdapter  Unit #com.bei.rag.adapter.NavGroupAdapter  View #com.bei.rag.adapter.NavGroupAdapter  TextView 6com.bei.rag.adapter.NavGroupAdapter.NavGroupViewHolder  View 6com.bei.rag.adapter.NavGroupAdapter.NavGroupViewHolder  AppDatabase com.bei.rag.database  ChatMessageEntity com.bei.rag.database  ConversationGroupEntity com.bei.rag.database  DocumentEntity com.bei.rag.database  StringListConverter com.bei.rag.database  
UserEntity com.bei.rag.database  Volatile com.bei.rag.database  AppDatabase  com.bei.rag.database.AppDatabase  ChatMessageDao  com.bei.rag.database.AppDatabase  Context  com.bei.rag.database.AppDatabase  ConversationGroupDao  com.bei.rag.database.AppDatabase  DocumentDao  com.bei.rag.database.AppDatabase  	Migration  com.bei.rag.database.AppDatabase  RoomDatabase  com.bei.rag.database.AppDatabase  SupportSQLiteDatabase  com.bei.rag.database.AppDatabase  UserDao  com.bei.rag.database.AppDatabase  Volatile  com.bei.rag.database.AppDatabase  AppDatabase *com.bei.rag.database.AppDatabase.Companion  ChatMessageDao *com.bei.rag.database.AppDatabase.Companion  Context *com.bei.rag.database.AppDatabase.Companion  ConversationGroupDao *com.bei.rag.database.AppDatabase.Companion  DocumentDao *com.bei.rag.database.AppDatabase.Companion  	Migration *com.bei.rag.database.AppDatabase.Companion  RoomDatabase *com.bei.rag.database.AppDatabase.Companion  SupportSQLiteDatabase *com.bei.rag.database.AppDatabase.Companion  UserDao *com.bei.rag.database.AppDatabase.Companion  Volatile *com.bei.rag.database.AppDatabase.Companion  SupportSQLiteDatabase ;com.bei.rag.database.AppDatabase.Companion.DatabaseCallback  List com.bei.rag.database.converter  String com.bei.rag.database.converter  StringListConverter com.bei.rag.database.converter  List 2com.bei.rag.database.converter.StringListConverter  String 2com.bei.rag.database.converter.StringListConverter  
TypeConverter 2com.bei.rag.database.converter.StringListConverter  Boolean com.bei.rag.database.dao  ChatMessageDao com.bei.rag.database.dao  ConversationGroupDao com.bei.rag.database.dao  Dao com.bei.rag.database.dao  Delete com.bei.rag.database.dao  DocumentDao com.bei.rag.database.dao  Insert com.bei.rag.database.dao  Int com.bei.rag.database.dao  List com.bei.rag.database.dao  Long com.bei.rag.database.dao  OnConflictStrategy com.bei.rag.database.dao  Query com.bei.rag.database.dao  String com.bei.rag.database.dao  Update com.bei.rag.database.dao  UserDao com.bei.rag.database.dao  ChatMessageEntity 'com.bei.rag.database.dao.ChatMessageDao  Delete 'com.bei.rag.database.dao.ChatMessageDao  Flow 'com.bei.rag.database.dao.ChatMessageDao  Insert 'com.bei.rag.database.dao.ChatMessageDao  Int 'com.bei.rag.database.dao.ChatMessageDao  List 'com.bei.rag.database.dao.ChatMessageDao  Long 'com.bei.rag.database.dao.ChatMessageDao  Query 'com.bei.rag.database.dao.ChatMessageDao  String 'com.bei.rag.database.dao.ChatMessageDao  ConversationGroupEntity -com.bei.rag.database.dao.ConversationGroupDao  Delete -com.bei.rag.database.dao.ConversationGroupDao  Flow -com.bei.rag.database.dao.ConversationGroupDao  Insert -com.bei.rag.database.dao.ConversationGroupDao  Int -com.bei.rag.database.dao.ConversationGroupDao  List -com.bei.rag.database.dao.ConversationGroupDao  Long -com.bei.rag.database.dao.ConversationGroupDao  Query -com.bei.rag.database.dao.ConversationGroupDao  Update -com.bei.rag.database.dao.ConversationGroupDao  Boolean $com.bei.rag.database.dao.DocumentDao  Delete $com.bei.rag.database.dao.DocumentDao  DocumentEntity $com.bei.rag.database.dao.DocumentDao  Flow $com.bei.rag.database.dao.DocumentDao  Insert $com.bei.rag.database.dao.DocumentDao  Int $com.bei.rag.database.dao.DocumentDao  List $com.bei.rag.database.dao.DocumentDao  Long $com.bei.rag.database.dao.DocumentDao  Query $com.bei.rag.database.dao.DocumentDao  String $com.bei.rag.database.dao.DocumentDao  Update $com.bei.rag.database.dao.DocumentDao  Flow  com.bei.rag.database.dao.UserDao  Insert  com.bei.rag.database.dao.UserDao  Long  com.bei.rag.database.dao.UserDao  OnConflictStrategy  com.bei.rag.database.dao.UserDao  Query  com.bei.rag.database.dao.UserDao  String  com.bei.rag.database.dao.UserDao  Update  com.bei.rag.database.dao.UserDao  
UserEntity  com.bei.rag.database.dao.UserDao  Boolean com.bei.rag.database.entity  ChatMessageEntity com.bei.rag.database.entity  ConversationGroupEntity com.bei.rag.database.entity  DocumentEntity com.bei.rag.database.entity  Int com.bei.rag.database.entity  List com.bei.rag.database.entity  Long com.bei.rag.database.entity  String com.bei.rag.database.entity  
UserEntity com.bei.rag.database.entity  Boolean -com.bei.rag.database.entity.ChatMessageEntity  List -com.bei.rag.database.entity.ChatMessageEntity  Long -com.bei.rag.database.entity.ChatMessageEntity  
PrimaryKey -com.bei.rag.database.entity.ChatMessageEntity  String -com.bei.rag.database.entity.ChatMessageEntity  Long 3com.bei.rag.database.entity.ConversationGroupEntity  
PrimaryKey 3com.bei.rag.database.entity.ConversationGroupEntity  String 3com.bei.rag.database.entity.ConversationGroupEntity  Boolean *com.bei.rag.database.entity.DocumentEntity  Int *com.bei.rag.database.entity.DocumentEntity  Long *com.bei.rag.database.entity.DocumentEntity  
PrimaryKey *com.bei.rag.database.entity.DocumentEntity  String *com.bei.rag.database.entity.DocumentEntity  Long &com.bei.rag.database.entity.UserEntity  
PrimaryKey &com.bei.rag.database.entity.UserEntity  String &com.bei.rag.database.entity.UserEntity  Button 'com.bei.rag.debug.StatusBarTestActivity  
ScrollView 'com.bei.rag.debug.StatusBarTestActivity  StatusBarTestHelper 'com.bei.rag.debug.StatusBarTestActivity  TextView 'com.bei.rag.debug.StatusBarTestActivity  Button com.bei.rag.fragment  ImageButton com.bei.rag.fragment  KnowledgeBaseManager com.bei.rag.fragment  LinearLayout com.bei.rag.fragment  Long com.bei.rag.fragment  OpenRouterApiService com.bei.rag.fragment  RagQueryEngine com.bei.rag.fragment  SeekBar com.bei.rag.fragment  String com.bei.rag.fragment  TextToSpeechService com.bei.rag.fragment  TextView com.bei.rag.fragment  VoiceToTextService com.bei.rag.fragment  android com.bei.rag.fragment  ChatAdapter !com.bei.rag.fragment.ChatFragment  ChatRepository !com.bei.rag.fragment.ChatFragment  ConversationGroupRepository !com.bei.rag.fragment.ChatFragment  DataManager !com.bei.rag.fragment.ChatFragment  DrawerLayout !com.bei.rag.fragment.ChatFragment  EditText !com.bei.rag.fragment.ChatFragment  ImageButton !com.bei.rag.fragment.ChatFragment  Job !com.bei.rag.fragment.ChatFragment  LinearLayout !com.bei.rag.fragment.ChatFragment  Long !com.bei.rag.fragment.ChatFragment  Markwon !com.bei.rag.fragment.ChatFragment  NavConversationAdapter !com.bei.rag.fragment.ChatFragment  NavGroupAdapter !com.bei.rag.fragment.ChatFragment  OpenRouterApiService !com.bei.rag.fragment.ChatFragment  ProgressBar !com.bei.rag.fragment.ChatFragment  RagQueryEngine !com.bei.rag.fragment.ChatFragment  RecyclerView !com.bei.rag.fragment.ChatFragment  String !com.bei.rag.fragment.ChatFragment  TextToSpeechService !com.bei.rag.fragment.ChatFragment  TextView !com.bei.rag.fragment.ChatFragment  	TtsConfig !com.bei.rag.fragment.ChatFragment  UserRepository !com.bei.rag.fragment.ChatFragment  VoiceToTextService !com.bei.rag.fragment.ChatFragment  ChatAdapter +com.bei.rag.fragment.ChatFragment.Companion  ChatRepository +com.bei.rag.fragment.ChatFragment.Companion  ConversationGroupRepository +com.bei.rag.fragment.ChatFragment.Companion  DataManager +com.bei.rag.fragment.ChatFragment.Companion  DrawerLayout +com.bei.rag.fragment.ChatFragment.Companion  EditText +com.bei.rag.fragment.ChatFragment.Companion  ImageButton +com.bei.rag.fragment.ChatFragment.Companion  Job +com.bei.rag.fragment.ChatFragment.Companion  LinearLayout +com.bei.rag.fragment.ChatFragment.Companion  Long +com.bei.rag.fragment.ChatFragment.Companion  Markwon +com.bei.rag.fragment.ChatFragment.Companion  NavConversationAdapter +com.bei.rag.fragment.ChatFragment.Companion  NavGroupAdapter +com.bei.rag.fragment.ChatFragment.Companion  OpenRouterApiService +com.bei.rag.fragment.ChatFragment.Companion  ProgressBar +com.bei.rag.fragment.ChatFragment.Companion  RagQueryEngine +com.bei.rag.fragment.ChatFragment.Companion  RecyclerView +com.bei.rag.fragment.ChatFragment.Companion  String +com.bei.rag.fragment.ChatFragment.Companion  TextToSpeechService +com.bei.rag.fragment.ChatFragment.Companion  TextView +com.bei.rag.fragment.ChatFragment.Companion  	TtsConfig +com.bei.rag.fragment.ChatFragment.Companion  UserRepository +com.bei.rag.fragment.ChatFragment.Companion  VoiceToTextService +com.bei.rag.fragment.ChatFragment.Companion  ConversationGroupAdapter /com.bei.rag.fragment.ConversationGroupsFragment  ConversationGroupRepository /com.bei.rag.fragment.ConversationGroupsFragment  ImageButton /com.bei.rag.fragment.ConversationGroupsFragment  LinearLayout /com.bei.rag.fragment.ConversationGroupsFragment  RecyclerView /com.bei.rag.fragment.ConversationGroupsFragment  TextView /com.bei.rag.fragment.ConversationGroupsFragment  ConversationGroupAdapter 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  ConversationGroupRepository 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  ImageButton 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  LinearLayout 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  RecyclerView 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  TextView 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  ChatRepository -com.bei.rag.fragment.ConversationListFragment  ConversationAdapter -com.bei.rag.fragment.ConversationListFragment  ImageButton -com.bei.rag.fragment.ConversationListFragment  Long -com.bei.rag.fragment.ConversationListFragment  RecyclerView -com.bei.rag.fragment.ConversationListFragment  TextView -com.bei.rag.fragment.ConversationListFragment  ChatRepository 7com.bei.rag.fragment.ConversationListFragment.Companion  ConversationAdapter 7com.bei.rag.fragment.ConversationListFragment.Companion  ImageButton 7com.bei.rag.fragment.ConversationListFragment.Companion  Long 7com.bei.rag.fragment.ConversationListFragment.Companion  RecyclerView 7com.bei.rag.fragment.ConversationListFragment.Companion  TextView 7com.bei.rag.fragment.ConversationListFragment.Companion  DocumentAdapter &com.bei.rag.fragment.KnowledgeFragment  DocumentRepository &com.bei.rag.fragment.KnowledgeFragment  KnowledgeBaseManager &com.bei.rag.fragment.KnowledgeFragment  RecyclerView &com.bei.rag.fragment.KnowledgeFragment  android &com.bei.rag.fragment.KnowledgeFragment  DocumentAdapter 0com.bei.rag.fragment.KnowledgeFragment.Companion  DocumentRepository 0com.bei.rag.fragment.KnowledgeFragment.Companion  KnowledgeBaseManager 0com.bei.rag.fragment.KnowledgeFragment.Companion  RecyclerView 0com.bei.rag.fragment.KnowledgeFragment.Companion  android 0com.bei.rag.fragment.KnowledgeFragment.Companion  Button $com.bei.rag.fragment.ProfileFragment  	ImageView $com.bei.rag.fragment.ProfileFragment  LinearLayout $com.bei.rag.fragment.ProfileFragment  SharedPreferences $com.bei.rag.fragment.ProfileFragment  TextView $com.bei.rag.fragment.ProfileFragment  UserRepository $com.bei.rag.fragment.ProfileFragment  Button .com.bei.rag.fragment.ProfileFragment.Companion  	ImageView .com.bei.rag.fragment.ProfileFragment.Companion  LinearLayout .com.bei.rag.fragment.ProfileFragment.Companion  SharedPreferences .com.bei.rag.fragment.ProfileFragment.Companion  TextView .com.bei.rag.fragment.ProfileFragment.Companion  UserRepository .com.bei.rag.fragment.ProfileFragment.Companion  DataManager %com.bei.rag.fragment.SettingsFragment  ImageButton %com.bei.rag.fragment.SettingsFragment  LinearLayout %com.bei.rag.fragment.SettingsFragment  SwitchCompat %com.bei.rag.fragment.SettingsFragment  	TtsConfig %com.bei.rag.fragment.SettingsFragment  DataManager /com.bei.rag.fragment.SettingsFragment.Companion  ImageButton /com.bei.rag.fragment.SettingsFragment.Companion  LinearLayout /com.bei.rag.fragment.SettingsFragment.Companion  SwitchCompat /com.bei.rag.fragment.SettingsFragment.Companion  	TtsConfig /com.bei.rag.fragment.SettingsFragment.Companion  ImageButton +com.bei.rag.fragment.SystemSettingsFragment  LinearLayout +com.bei.rag.fragment.SystemSettingsFragment  Switch +com.bei.rag.fragment.SystemSettingsFragment  ImageButton 5com.bei.rag.fragment.SystemSettingsFragment.Companion  LinearLayout 5com.bei.rag.fragment.SystemSettingsFragment.Companion  Switch 5com.bei.rag.fragment.SystemSettingsFragment.Companion  ImageButton /com.bei.rag.fragment.ThemeColorSettingsFragment  	ImageView /com.bei.rag.fragment.ThemeColorSettingsFragment  LinearLayout /com.bei.rag.fragment.ThemeColorSettingsFragment  ThemeManager /com.bei.rag.fragment.ThemeColorSettingsFragment  ImageButton 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  	ImageView 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  LinearLayout 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  ThemeManager 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  ImageButton *com.bei.rag.fragment.ThemeSettingsFragment  	ImageView *com.bei.rag.fragment.ThemeSettingsFragment  LinearLayout *com.bei.rag.fragment.ThemeSettingsFragment  RadioButton *com.bei.rag.fragment.ThemeSettingsFragment  TextView *com.bei.rag.fragment.ThemeSettingsFragment  ThemeManager *com.bei.rag.fragment.ThemeSettingsFragment  ImageButton 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  	ImageView 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  LinearLayout 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  RadioButton 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  TextView 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  ThemeManager 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  Button 0com.bei.rag.fragment.VoiceEngineSettingsFragment  ImageButton 0com.bei.rag.fragment.VoiceEngineSettingsFragment  LinearLayout 0com.bei.rag.fragment.VoiceEngineSettingsFragment  SeekBar 0com.bei.rag.fragment.VoiceEngineSettingsFragment  TextToSpeechService 0com.bei.rag.fragment.VoiceEngineSettingsFragment  TextView 0com.bei.rag.fragment.VoiceEngineSettingsFragment  	TtsConfig 0com.bei.rag.fragment.VoiceEngineSettingsFragment  Button :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  ImageButton :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  LinearLayout :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  SeekBar :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  TextToSpeechService :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  TextView :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  	TtsConfig :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  Any com.bei.rag.model  Boolean com.bei.rag.model  ChatMessage com.bei.rag.model  Choice com.bei.rag.model  Content com.bei.rag.model  Conversation com.bei.rag.model  ConversationGroup com.bei.rag.model  
EmbeddingData com.bei.rag.model  EmbeddingUsage com.bei.rag.model  Float com.bei.rag.model  Int com.bei.rag.model  List com.bei.rag.model  Long com.bei.rag.model  Map com.bei.rag.model  Message com.bei.rag.model  OkHttpClient com.bei.rag.model  OpenRouterApiService com.bei.rag.model  Pair com.bei.rag.model  RagQueryEngine com.bei.rag.model  ResponseMessage com.bei.rag.model  String com.bei.rag.model  TextToSpeechService com.bei.rag.model  TtsCandidate com.bei.rag.model  
TtsContent com.bei.rag.model  TtsError com.bei.rag.model  TtsGenerationConfig com.bei.rag.model  
TtsInlineData com.bei.rag.model  TtsPart com.bei.rag.model  TtsPrebuiltVoiceConfig com.bei.rag.model  
TtsRequest com.bei.rag.model  TtsResponse com.bei.rag.model  TtsResponseContent com.bei.rag.model  TtsResponsePart com.bei.rag.model  TtsSpeechConfig com.bei.rag.model  TtsVoiceConfig com.bei.rag.model  Usage com.bei.rag.model  VoiceToTextResponse com.bei.rag.model  VoiceToTextService com.bei.rag.model  Boolean &com.bei.rag.model.BatchOperationResult  Int &com.bei.rag.model.BatchOperationResult  List &com.bei.rag.model.BatchOperationResult  Pair &com.bei.rag.model.BatchOperationResult  String &com.bei.rag.model.BatchOperationResult  Boolean com.bei.rag.model.ChatMessage  List com.bei.rag.model.ChatMessage  Long com.bei.rag.model.ChatMessage  String com.bei.rag.model.ChatMessage  List com.bei.rag.model.ChatRequest  Message com.bei.rag.model.ChatRequest  SerializedName com.bei.rag.model.ChatRequest  String com.bei.rag.model.ChatRequest  Choice com.bei.rag.model.ChatResponse  List com.bei.rag.model.ChatResponse  Long com.bei.rag.model.ChatResponse  SerializedName com.bei.rag.model.ChatResponse  String com.bei.rag.model.ChatResponse  Usage com.bei.rag.model.ChatResponse  Any com.bei.rag.model.Choice  Int com.bei.rag.model.Choice  ResponseMessage com.bei.rag.model.Choice  SerializedName com.bei.rag.model.Choice  String com.bei.rag.model.Choice  SerializedName com.bei.rag.model.Content  String com.bei.rag.model.Content  Float com.bei.rag.model.EmbeddingData  Int com.bei.rag.model.EmbeddingData  List com.bei.rag.model.EmbeddingData  SerializedName com.bei.rag.model.EmbeddingData  String com.bei.rag.model.EmbeddingData  SerializedName "com.bei.rag.model.EmbeddingRequest  String "com.bei.rag.model.EmbeddingRequest  
EmbeddingData #com.bei.rag.model.EmbeddingResponse  EmbeddingUsage #com.bei.rag.model.EmbeddingResponse  List #com.bei.rag.model.EmbeddingResponse  SerializedName #com.bei.rag.model.EmbeddingResponse  String #com.bei.rag.model.EmbeddingResponse  Int  com.bei.rag.model.EmbeddingUsage  SerializedName  com.bei.rag.model.EmbeddingUsage  Content com.bei.rag.model.Message  List com.bei.rag.model.Message  SerializedName com.bei.rag.model.Message  String com.bei.rag.model.Message  Any !com.bei.rag.model.ResponseMessage  SerializedName !com.bei.rag.model.ResponseMessage  String !com.bei.rag.model.ResponseMessage  Boolean "com.bei.rag.model.SupabaseDocument  Int "com.bei.rag.model.SupabaseDocument  Long "com.bei.rag.model.SupabaseDocument  
SerialName "com.bei.rag.model.SupabaseDocument  String "com.bei.rag.model.SupabaseDocument  Float 'com.bei.rag.model.SupabaseDocumentChunk  Int 'com.bei.rag.model.SupabaseDocumentChunk  List 'com.bei.rag.model.SupabaseDocumentChunk  Long 'com.bei.rag.model.SupabaseDocumentChunk  Map 'com.bei.rag.model.SupabaseDocumentChunk  
SerialName 'com.bei.rag.model.SupabaseDocumentChunk  String 'com.bei.rag.model.SupabaseDocumentChunk  SerializedName com.bei.rag.model.TtsCandidate  TtsResponseContent com.bei.rag.model.TtsCandidate  List com.bei.rag.model.TtsContent  SerializedName com.bei.rag.model.TtsContent  TtsPart com.bei.rag.model.TtsContent  Int com.bei.rag.model.TtsError  SerializedName com.bei.rag.model.TtsError  String com.bei.rag.model.TtsError  SerializedName %com.bei.rag.model.TtsGenerationConfig  TtsSpeechConfig %com.bei.rag.model.TtsGenerationConfig  SerializedName com.bei.rag.model.TtsInlineData  String com.bei.rag.model.TtsInlineData  SerializedName com.bei.rag.model.TtsPart  String com.bei.rag.model.TtsPart  SerializedName (com.bei.rag.model.TtsPrebuiltVoiceConfig  String (com.bei.rag.model.TtsPrebuiltVoiceConfig  List com.bei.rag.model.TtsRequest  SerializedName com.bei.rag.model.TtsRequest  
TtsContent com.bei.rag.model.TtsRequest  TtsGenerationConfig com.bei.rag.model.TtsRequest  List com.bei.rag.model.TtsResponse  SerializedName com.bei.rag.model.TtsResponse  TtsCandidate com.bei.rag.model.TtsResponse  TtsError com.bei.rag.model.TtsResponse  List $com.bei.rag.model.TtsResponseContent  SerializedName $com.bei.rag.model.TtsResponseContent  TtsResponsePart $com.bei.rag.model.TtsResponseContent  SerializedName !com.bei.rag.model.TtsResponsePart  
TtsInlineData !com.bei.rag.model.TtsResponsePart  SerializedName !com.bei.rag.model.TtsSpeechConfig  TtsVoiceConfig !com.bei.rag.model.TtsSpeechConfig  SerializedName  com.bei.rag.model.TtsVoiceConfig  TtsPrebuiltVoiceConfig  com.bei.rag.model.TtsVoiceConfig  Int com.bei.rag.model.Usage  SerializedName com.bei.rag.model.Usage  ChatRepository com.bei.rag.repository  ConversationGroupRepository com.bei.rag.repository  DocumentRepository com.bei.rag.repository  UserRepository com.bei.rag.repository  Boolean com.bei.rag.service  GeminiTtsApiService com.bei.rag.service  Header com.bei.rag.service  KnowledgeBaseManager com.bei.rag.service  OkHttpClient com.bei.rag.service  OpenRouterApiService com.bei.rag.service  Part com.bei.rag.service  RagQueryEngine com.bei.rag.service  SiliconFlowApiService com.bei.rag.service  String com.bei.rag.service  TextToSpeechService com.bei.rag.service  VoiceToTextService com.bei.rag.service  android com.bei.rag.service  Body 'com.bei.rag.service.GeminiTtsApiService  Query 'com.bei.rag.service.GeminiTtsApiService  Response 'com.bei.rag.service.GeminiTtsApiService  String 'com.bei.rag.service.GeminiTtsApiService  
TtsRequest 'com.bei.rag.service.GeminiTtsApiService  TtsResponse 'com.bei.rag.service.GeminiTtsApiService  OkHttpClient (com.bei.rag.service.OpenRouterApiService  String (com.bei.rag.service.OpenRouterApiService  Header )com.bei.rag.service.SiliconFlowApiService  
MultipartBody )com.bei.rag.service.SiliconFlowApiService  Part )com.bei.rag.service.SiliconFlowApiService  RequestBody )com.bei.rag.service.SiliconFlowApiService  Response )com.bei.rag.service.SiliconFlowApiService  String )com.bei.rag.service.SiliconFlowApiService  VoiceToTextResponse )com.bei.rag.service.SiliconFlowApiService  OkHttpClient /com.bei.rag.service.SiliconFlowEmbeddingService  String /com.bei.rag.service.SiliconFlowEmbeddingService  Boolean "com.bei.rag.service.SupabaseConfig  SupabaseClient "com.bei.rag.service.SupabaseConfig  Context 'com.bei.rag.service.TextToSpeechService  	TtsEngine 'com.bei.rag.service.TextToSpeechService  Context 1com.bei.rag.service.TextToSpeechService.Companion  	TtsEngine 1com.bei.rag.service.TextToSpeechService.Companion  AudioRecord &com.bei.rag.service.VoiceToTextService  Context &com.bei.rag.service.VoiceToTextService  File &com.bei.rag.service.VoiceToTextService  SiliconFlowApiService &com.bei.rag.service.VoiceToTextService  Float com.bei.rag.service.tts  String com.bei.rag.service.tts  	TtsEngine com.bei.rag.service.tts  Unit com.bei.rag.service.tts  Context (com.bei.rag.service.tts.AndroidTtsEngine  String (com.bei.rag.service.tts.AndroidTtsEngine  TextToSpeech (com.bei.rag.service.tts.AndroidTtsEngine  Unit (com.bei.rag.service.tts.AndroidTtsEngine  Context 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  String 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  TextToSpeech 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  Unit 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  Context 'com.bei.rag.service.tts.GeminiTtsEngine  File 'com.bei.rag.service.tts.GeminiTtsEngine  Float 'com.bei.rag.service.tts.GeminiTtsEngine  GeminiTtsApiService 'com.bei.rag.service.tts.GeminiTtsEngine  MediaPlayer 'com.bei.rag.service.tts.GeminiTtsEngine  String 'com.bei.rag.service.tts.GeminiTtsEngine  Unit 'com.bei.rag.service.tts.GeminiTtsEngine  Context 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  File 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  Float 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  GeminiTtsApiService 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  MediaPlayer 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  String 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  Unit 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  Boolean com.bei.rag.utils  DataManager com.bei.rag.utils  Float com.bei.rag.utils  Int com.bei.rag.utils  Long com.bei.rag.utils  StatusBarManager com.bei.rag.utils  StatusBarTestHelper com.bei.rag.utils  String com.bei.rag.utils  ThemeManager com.bei.rag.utils  	TtsConfig com.bei.rag.utils  Unit com.bei.rag.utils  android com.bei.rag.utils  Context com.bei.rag.utils.DataManager  Activity +com.bei.rag.utils.StatusBarAnimationManager  AnimationListener +com.bei.rag.utils.StatusBarAnimationManager  Boolean +com.bei.rag.utils.StatusBarAnimationManager  ColorInt +com.bei.rag.utils.StatusBarAnimationManager  Float +com.bei.rag.utils.StatusBarAnimationManager  Int +com.bei.rag.utils.StatusBarAnimationManager  Long +com.bei.rag.utils.StatusBarAnimationManager  Unit +com.bei.rag.utils.StatusBarAnimationManager  
ValueAnimator +com.bei.rag.utils.StatusBarAnimationManager  View +com.bei.rag.utils.StatusBarAnimationManager  Window +com.bei.rag.utils.StatusBarAnimationManager  Activity 5com.bei.rag.utils.StatusBarAnimationManager.Companion  Boolean 5com.bei.rag.utils.StatusBarAnimationManager.Companion  ColorInt 5com.bei.rag.utils.StatusBarAnimationManager.Companion  Float 5com.bei.rag.utils.StatusBarAnimationManager.Companion  Int 5com.bei.rag.utils.StatusBarAnimationManager.Companion  Long 5com.bei.rag.utils.StatusBarAnimationManager.Companion  Unit 5com.bei.rag.utils.StatusBarAnimationManager.Companion  
ValueAnimator 5com.bei.rag.utils.StatusBarAnimationManager.Companion  View 5com.bei.rag.utils.StatusBarAnimationManager.Companion  Window 5com.bei.rag.utils.StatusBarAnimationManager.Companion  Boolean 'com.bei.rag.utils.StatusBarColorAdapter  ColorInt 'com.bei.rag.utils.StatusBarColorAdapter  Context 'com.bei.rag.utils.StatusBarColorAdapter  Float 'com.bei.rag.utils.StatusBarColorAdapter  Int 'com.bei.rag.utils.StatusBarColorAdapter  String 'com.bei.rag.utils.StatusBarColorAdapter  Boolean 7com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig  ColorInt 7com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig  Int 7com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig  String 7com.bei.rag.utils.StatusBarColorAdapter.StatusBarConfig  Activity (com.bei.rag.utils.StatusBarCompatManager  Boolean (com.bei.rag.utils.StatusBarCompatManager  ColorInt (com.bei.rag.utils.StatusBarCompatManager  Int (com.bei.rag.utils.StatusBarCompatManager  StatusBarStrategy (com.bei.rag.utils.StatusBarCompatManager  Window (com.bei.rag.utils.StatusBarCompatManager  Activity 2com.bei.rag.utils.StatusBarCompatManager.Companion  Boolean 2com.bei.rag.utils.StatusBarCompatManager.Companion  ColorInt 2com.bei.rag.utils.StatusBarCompatManager.Companion  Int 2com.bei.rag.utils.StatusBarCompatManager.Companion  Window 2com.bei.rag.utils.StatusBarCompatManager.Companion  Activity "com.bei.rag.utils.StatusBarManager  Boolean "com.bei.rag.utils.StatusBarManager  ColorInt "com.bei.rag.utils.StatusBarManager  Context "com.bei.rag.utils.StatusBarManager  Float "com.bei.rag.utils.StatusBarManager  Int "com.bei.rag.utils.StatusBarManager  Window "com.bei.rag.utils.StatusBarManager  Activity ,com.bei.rag.utils.StatusBarManager.Companion  Boolean ,com.bei.rag.utils.StatusBarManager.Companion  ColorInt ,com.bei.rag.utils.StatusBarManager.Companion  Context ,com.bei.rag.utils.StatusBarManager.Companion  Float ,com.bei.rag.utils.StatusBarManager.Companion  Int ,com.bei.rag.utils.StatusBarManager.Companion  Window ,com.bei.rag.utils.StatusBarManager.Companion  Activity )com.bei.rag.utils.StatusBarOverlayManager  Boolean )com.bei.rag.utils.StatusBarOverlayManager  ColorInt )com.bei.rag.utils.StatusBarOverlayManager  Float )com.bei.rag.utils.StatusBarOverlayManager  Int )com.bei.rag.utils.StatusBarOverlayManager  View )com.bei.rag.utils.StatusBarOverlayManager  	ViewGroup )com.bei.rag.utils.StatusBarOverlayManager  android )com.bei.rag.utils.StatusBarOverlayManager  Activity 3com.bei.rag.utils.StatusBarOverlayManager.Companion  Boolean 3com.bei.rag.utils.StatusBarOverlayManager.Companion  ColorInt 3com.bei.rag.utils.StatusBarOverlayManager.Companion  Float 3com.bei.rag.utils.StatusBarOverlayManager.Companion  Int 3com.bei.rag.utils.StatusBarOverlayManager.Companion  View 3com.bei.rag.utils.StatusBarOverlayManager.Companion  	ViewGroup 3com.bei.rag.utils.StatusBarOverlayManager.Companion  android 3com.bei.rag.utils.StatusBarOverlayManager.Companion  Context %com.bei.rag.utils.StatusBarTestHelper  Context /com.bei.rag.utils.StatusBarTestHelper.Companion  Context com.bei.rag.utils.ThemeManager  SharedPreferences com.bei.rag.utils.ThemeManager  Context (com.bei.rag.utils.ThemeManager.Companion  SharedPreferences (com.bei.rag.utils.ThemeManager.Companion  Context com.bei.rag.utils.TtsConfig  SharedPreferences com.bei.rag.utils.TtsConfig  Context %com.bei.rag.utils.TtsConfig.Companion  SharedPreferences %com.bei.rag.utils.TtsConfig.Companion  SerializedName com.google.gson.annotations  SupabaseClient io.github.jan.supabase  Markwon io.noties.markwon  File java.io  ChatMessageEntity 	java.lang  ConversationGroupEntity 	java.lang  DocumentEntity 	java.lang  OnConflictStrategy 	java.lang  StringListConverter 	java.lang  
UserEntity 	java.lang  android 	java.lang  Any kotlin  Array kotlin  Boolean kotlin  ChatMessageEntity kotlin  ConversationGroupEntity kotlin  DocumentEntity kotlin  Float kotlin  Int kotlin  Long kotlin  OnConflictStrategy kotlin  Pair kotlin  String kotlin  StringListConverter kotlin  Unit kotlin  
UserEntity kotlin  Volatile kotlin  android kotlin  arrayOf kotlin  ChatMessageEntity kotlin.annotation  ConversationGroupEntity kotlin.annotation  DocumentEntity kotlin.annotation  OnConflictStrategy kotlin.annotation  Pair kotlin.annotation  StringListConverter kotlin.annotation  
UserEntity kotlin.annotation  Volatile kotlin.annotation  android kotlin.annotation  ChatMessageEntity kotlin.collections  ConversationGroupEntity kotlin.collections  DocumentEntity kotlin.collections  List kotlin.collections  Map kotlin.collections  OnConflictStrategy kotlin.collections  Pair kotlin.collections  StringListConverter kotlin.collections  
UserEntity kotlin.collections  Volatile kotlin.collections  android kotlin.collections  ChatMessageEntity kotlin.comparisons  ConversationGroupEntity kotlin.comparisons  DocumentEntity kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Pair kotlin.comparisons  StringListConverter kotlin.comparisons  
UserEntity kotlin.comparisons  Volatile kotlin.comparisons  android kotlin.comparisons  ChatMessageEntity 	kotlin.io  ConversationGroupEntity 	kotlin.io  DocumentEntity 	kotlin.io  OnConflictStrategy 	kotlin.io  Pair 	kotlin.io  StringListConverter 	kotlin.io  
UserEntity 	kotlin.io  Volatile 	kotlin.io  android 	kotlin.io  ChatMessageEntity 
kotlin.jvm  ConversationGroupEntity 
kotlin.jvm  DocumentEntity 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Pair 
kotlin.jvm  StringListConverter 
kotlin.jvm  
UserEntity 
kotlin.jvm  Volatile 
kotlin.jvm  android 
kotlin.jvm  ChatMessageEntity 
kotlin.ranges  ConversationGroupEntity 
kotlin.ranges  DocumentEntity 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Pair 
kotlin.ranges  StringListConverter 
kotlin.ranges  
UserEntity 
kotlin.ranges  Volatile 
kotlin.ranges  android 
kotlin.ranges  KClass kotlin.reflect  ChatMessageEntity kotlin.sequences  ConversationGroupEntity kotlin.sequences  DocumentEntity kotlin.sequences  OnConflictStrategy kotlin.sequences  Pair kotlin.sequences  StringListConverter kotlin.sequences  
UserEntity kotlin.sequences  Volatile kotlin.sequences  android kotlin.sequences  ChatMessageEntity kotlin.text  ConversationGroupEntity kotlin.text  DocumentEntity kotlin.text  OnConflictStrategy kotlin.text  Pair kotlin.text  StringListConverter kotlin.text  
UserEntity kotlin.text  Volatile kotlin.text  android kotlin.text  Job kotlinx.coroutines  Flow kotlinx.coroutines.flow  
SerialName kotlinx.serialization  Serializable kotlinx.serialization  
MultipartBody okhttp3  OkHttpClient okhttp3  RequestBody okhttp3  Part okhttp3.MultipartBody  Response 	retrofit2  Body retrofit2.http  Header retrofit2.http  Part retrofit2.http  Query retrofit2.http  Bundle android.app.Activity  Bundle android.content.Context  Bundle android.content.ContextWrapper  Bundle 
android.os  LayoutInflater android.view  Bundle  android.view.ContextThemeWrapper  Bundle #androidx.activity.ComponentActivity  Bundle (androidx.appcompat.app.AppCompatActivity  Bundle #androidx.core.app.ComponentActivity  Bundle androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  Bundle &androidx.fragment.app.FragmentActivity  Fragment 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  FragmentStateAdapter androidx.viewpager2.adapter  Fragment 0androidx.viewpager2.adapter.FragmentStateAdapter  Int 0androidx.viewpager2.adapter.FragmentStateAdapter  Bundle com.bei.rag.ChatScreen  Int com.bei.rag.adapter  Int com.bei.rag.adapter.ChatAdapter  	ViewGroup com.bei.rag.adapter.ChatAdapter  Int )com.bei.rag.adapter.ChatAdapter.Companion  	ViewGroup )com.bei.rag.adapter.ChatAdapter.Companion  Int 'com.bei.rag.adapter.ConversationAdapter  	ViewGroup 'com.bei.rag.adapter.ConversationAdapter  Int ,com.bei.rag.adapter.ConversationGroupAdapter  	ViewGroup ,com.bei.rag.adapter.ConversationGroupAdapter  Int #com.bei.rag.adapter.DocumentAdapter  	ViewGroup #com.bei.rag.adapter.DocumentAdapter  Fragment $com.bei.rag.adapter.MainPagerAdapter  Int $com.bei.rag.adapter.MainPagerAdapter  Int *com.bei.rag.adapter.NavConversationAdapter  	ViewGroup *com.bei.rag.adapter.NavConversationAdapter  Int #com.bei.rag.adapter.NavGroupAdapter  	ViewGroup #com.bei.rag.adapter.NavGroupAdapter  Bundle 'com.bei.rag.debug.StatusBarTestActivity  Bundle !com.bei.rag.fragment.ChatFragment  LayoutInflater !com.bei.rag.fragment.ChatFragment  View !com.bei.rag.fragment.ChatFragment  	ViewGroup !com.bei.rag.fragment.ChatFragment  Bundle +com.bei.rag.fragment.ChatFragment.Companion  LayoutInflater +com.bei.rag.fragment.ChatFragment.Companion  View +com.bei.rag.fragment.ChatFragment.Companion  	ViewGroup +com.bei.rag.fragment.ChatFragment.Companion  Bundle /com.bei.rag.fragment.ConversationGroupsFragment  LayoutInflater /com.bei.rag.fragment.ConversationGroupsFragment  View /com.bei.rag.fragment.ConversationGroupsFragment  	ViewGroup /com.bei.rag.fragment.ConversationGroupsFragment  Bundle 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  LayoutInflater 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  View 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  	ViewGroup 9com.bei.rag.fragment.ConversationGroupsFragment.Companion  Bundle -com.bei.rag.fragment.ConversationListFragment  LayoutInflater -com.bei.rag.fragment.ConversationListFragment  View -com.bei.rag.fragment.ConversationListFragment  	ViewGroup -com.bei.rag.fragment.ConversationListFragment  Bundle 7com.bei.rag.fragment.ConversationListFragment.Companion  LayoutInflater 7com.bei.rag.fragment.ConversationListFragment.Companion  View 7com.bei.rag.fragment.ConversationListFragment.Companion  	ViewGroup 7com.bei.rag.fragment.ConversationListFragment.Companion  Bundle &com.bei.rag.fragment.KnowledgeFragment  LayoutInflater &com.bei.rag.fragment.KnowledgeFragment  View &com.bei.rag.fragment.KnowledgeFragment  	ViewGroup &com.bei.rag.fragment.KnowledgeFragment  Bundle 0com.bei.rag.fragment.KnowledgeFragment.Companion  LayoutInflater 0com.bei.rag.fragment.KnowledgeFragment.Companion  View 0com.bei.rag.fragment.KnowledgeFragment.Companion  	ViewGroup 0com.bei.rag.fragment.KnowledgeFragment.Companion  Bundle $com.bei.rag.fragment.ProfileFragment  LayoutInflater $com.bei.rag.fragment.ProfileFragment  View $com.bei.rag.fragment.ProfileFragment  	ViewGroup $com.bei.rag.fragment.ProfileFragment  Bundle .com.bei.rag.fragment.ProfileFragment.Companion  LayoutInflater .com.bei.rag.fragment.ProfileFragment.Companion  View .com.bei.rag.fragment.ProfileFragment.Companion  	ViewGroup .com.bei.rag.fragment.ProfileFragment.Companion  Bundle %com.bei.rag.fragment.SettingsFragment  LayoutInflater %com.bei.rag.fragment.SettingsFragment  View %com.bei.rag.fragment.SettingsFragment  	ViewGroup %com.bei.rag.fragment.SettingsFragment  Bundle /com.bei.rag.fragment.SettingsFragment.Companion  LayoutInflater /com.bei.rag.fragment.SettingsFragment.Companion  View /com.bei.rag.fragment.SettingsFragment.Companion  	ViewGroup /com.bei.rag.fragment.SettingsFragment.Companion  Bundle +com.bei.rag.fragment.SystemSettingsFragment  LayoutInflater +com.bei.rag.fragment.SystemSettingsFragment  View +com.bei.rag.fragment.SystemSettingsFragment  	ViewGroup +com.bei.rag.fragment.SystemSettingsFragment  Bundle 5com.bei.rag.fragment.SystemSettingsFragment.Companion  LayoutInflater 5com.bei.rag.fragment.SystemSettingsFragment.Companion  View 5com.bei.rag.fragment.SystemSettingsFragment.Companion  	ViewGroup 5com.bei.rag.fragment.SystemSettingsFragment.Companion  Bundle /com.bei.rag.fragment.ThemeColorSettingsFragment  LayoutInflater /com.bei.rag.fragment.ThemeColorSettingsFragment  View /com.bei.rag.fragment.ThemeColorSettingsFragment  	ViewGroup /com.bei.rag.fragment.ThemeColorSettingsFragment  Bundle 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  LayoutInflater 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  View 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  	ViewGroup 9com.bei.rag.fragment.ThemeColorSettingsFragment.Companion  Bundle *com.bei.rag.fragment.ThemeSettingsFragment  LayoutInflater *com.bei.rag.fragment.ThemeSettingsFragment  View *com.bei.rag.fragment.ThemeSettingsFragment  	ViewGroup *com.bei.rag.fragment.ThemeSettingsFragment  Bundle 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  LayoutInflater 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  View 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  	ViewGroup 4com.bei.rag.fragment.ThemeSettingsFragment.Companion  Bundle 0com.bei.rag.fragment.VoiceEngineSettingsFragment  LayoutInflater 0com.bei.rag.fragment.VoiceEngineSettingsFragment  View 0com.bei.rag.fragment.VoiceEngineSettingsFragment  	ViewGroup 0com.bei.rag.fragment.VoiceEngineSettingsFragment  Bundle :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  LayoutInflater :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  View :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  	ViewGroup :com.bei.rag.fragment.VoiceEngineSettingsFragment.Companion  DocumentContent com.bei.rag.model  
FloatArray com.bei.rag.model  Result com.bei.rag.model  Any com.bei.rag.model.DocumentChunk  Boolean com.bei.rag.model.DocumentChunk  
FloatArray com.bei.rag.model.DocumentChunk  Int com.bei.rag.model.DocumentChunk  Long com.bei.rag.model.DocumentChunk  Map com.bei.rag.model.DocumentChunk  String com.bei.rag.model.DocumentChunk  DocumentParser com.bei.rag.service  List com.bei.rag.service  DocumentContent com.bei.rag.service.CsvParser  List com.bei.rag.service.CsvParser  String com.bei.rag.service.CsvParser  DocumentContent "com.bei.rag.service.DocumentParser  List "com.bei.rag.service.DocumentParser  String "com.bei.rag.service.DocumentParser  DocumentContent com.bei.rag.service.DocxParser  List com.bei.rag.service.DocxParser  String com.bei.rag.service.DocxParser  DocumentContent com.bei.rag.service.PdfParser  List com.bei.rag.service.PdfParser  String com.bei.rag.service.PdfParser  DocumentContent com.bei.rag.service.TxtParser  List com.bei.rag.service.TxtParser  String com.bei.rag.service.TxtParser  Boolean com.bei.rag.service.tts  Int com.bei.rag.service.tts  Result com.bei.rag.service.tts  Boolean (com.bei.rag.service.tts.AndroidTtsEngine  Float (com.bei.rag.service.tts.AndroidTtsEngine  Int (com.bei.rag.service.tts.AndroidTtsEngine  Result (com.bei.rag.service.tts.AndroidTtsEngine  
TtsEngineType (com.bei.rag.service.tts.AndroidTtsEngine  Boolean 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  Float 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  Int 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  Result 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  
TtsEngineType 2com.bei.rag.service.tts.AndroidTtsEngine.Companion  Boolean 'com.bei.rag.service.tts.GeminiTtsEngine  Result 'com.bei.rag.service.tts.GeminiTtsEngine  
TtsEngineType 'com.bei.rag.service.tts.GeminiTtsEngine  Boolean 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  Result 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  
TtsEngineType 1com.bei.rag.service.tts.GeminiTtsEngine.Companion  Boolean !com.bei.rag.service.tts.TtsEngine  Float !com.bei.rag.service.tts.TtsEngine  Result !com.bei.rag.service.tts.TtsEngine  String !com.bei.rag.service.tts.TtsEngine  
TtsEngineType !com.bei.rag.service.tts.TtsEngine  Unit !com.bei.rag.service.tts.TtsEngine  
TtsEngineType com.bei.rag.utils  Result 	java.util  
FloatArray kotlin  Result kotlin  Result kotlin.annotation  Result kotlin.collections  Result kotlin.comparisons  Result 	kotlin.io  Result 
kotlin.jvm  Result 
kotlin.ranges  Result kotlin.sequences  Result kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                