<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme for dark mode. -->
    <style name="Theme.Ragandroid" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- 状态栏配置 - 深色主题优化 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowTranslucentStatus" tools:targetApi="kitkat">false</item>
        <!-- 确保软键盘模式不被覆盖 -->
        <item name="android:windowSoftInputMode">adjustResize</item>

        <!-- 深色模式默认主题色 - 蓝色 -->
        <item name="colorThemePrimary">@color/ios_blue</item>
        <item name="colorThemePrimaryDark">@color/ios_blue_dark</item>
        <item name="colorThemePrimaryLight">@color/ios_blue_light</item>
        <item name="colorHeader">@color/ios_blue</item>
        <item name="colorHeaderDark">@color/ios_blue_dark</item>
        <item name="colorAccent">@color/ios_blue</item>
        <item name="colorTabSelected">@color/ios_blue</item>
        <item name="colorUserMessageBg">@color/ios_blue</item>
        <item name="colorProfileHeader">@color/ios_blue</item>
        <item name="colorFileIconDoc">@color/ios_blue</item>
        <item name="colorSendButton">@color/ios_blue</item>
        <item name="colorSendButtonPressed">@color/ios_blue_dark</item>
        <item name="colorBadgeBackground">@color/ios_blue</item>

        <!-- 深色模式背景和文字颜色 -->
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>

    <!-- 深色模式灰色主题 -->
    <style name="Theme.Ragandroid.Gray" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- 确保软键盘模式不被覆盖 -->
        <item name="android:windowSoftInputMode">adjustResize</item>

        <!-- 深色模式灰色主题色 -->
        <item name="colorThemePrimary">@color/theme_gray</item>
        <item name="colorThemePrimaryDark">@color/theme_gray_dark</item>
        <item name="colorThemePrimaryLight">@color/theme_gray_light</item>
        <item name="colorHeader">@color/theme_gray</item>
        <item name="colorHeaderDark">@color/theme_gray_dark</item>
        <item name="colorAccent">@color/theme_gray</item>
        <item name="colorTabSelected">@color/theme_gray</item>
        <item name="colorUserMessageBg">@color/theme_gray</item>
        <item name="colorProfileHeader">@color/theme_gray</item>
        <item name="colorFileIconDoc">@color/theme_gray</item>
        <item name="colorSendButton">@color/theme_gray</item>
        <item name="colorSendButtonPressed">@color/theme_gray_dark</item>
        <item name="colorBadgeBackground">@color/theme_gray</item>

        <!-- 深色模式背景和文字颜色 -->
        <item name="colorAppBackground">@color/ios_background</item>
        <item name="colorCardBackground">@color/ios_card_background</item>
        <item name="colorTextPrimary">@color/ios_text_primary</item>
        <item name="colorTextSecondary">@color/ios_text_secondary</item>
        <item name="colorBorder">@color/ios_border</item>
    </style>
</resources>